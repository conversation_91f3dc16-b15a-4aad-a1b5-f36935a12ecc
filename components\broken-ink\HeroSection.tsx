import React from "react";
import { Parallax } from "react-parallax";
import { UserProfile } from "@/types/user";
import { transformHeroData } from "@/lib/brokenInkUtils";
import { Button } from "@/components/ui/button";
import { getIconComponent } from "@/lib/iconUtils";
import { ArrowDown } from "lucide-react";

interface HeroSectionProps {
  profile: UserProfile;
}

const HeroSection = ({ profile }: HeroSectionProps) => {
  const heroData = transformHeroData(profile);
  const WhatsAppIcon = getIconComponent("whatsapp");

  // Find WhatsApp link from social media or use contact info
  const getWhatsAppUrl = () => {
    // First, try to find WhatsApp in social media links
    const whatsappSocial = profile.socialMedia?.find(
      (social) =>
        social.url.includes("whatsapp") || social.url.includes("wa.me")
    );

    if (whatsappSocial) {
      return whatsappSocial.url;
    }

    // Then try location contact WhatsApp
    if (profile.location?.contact?.whatsapp) {
      return `https://wa.me/${profile.location.contact.whatsapp.replace(
        /\D/g,
        ""
      )}`;
    }

    // Finally, try phone number
    if (profile.phone) {
      return `https://wa.me/${profile.phone.replace(/\D/g, "")}`;
    }

    // Default fallback
    return "https://wa.me/";
  };

  const handleWhatsAppClick = () => {
    const whatsappUrl = getWhatsAppUrl();
    window.open(whatsappUrl, "_blank");
  };

  const handleScrollToSocialLinks = () => {
    const socialLinksElement = document.getElementById("socialinks");
    if (socialLinksElement) {
      socialLinksElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  };

  return (
    <Parallax
      bgImage={heroData.backgroundImage}
      bgImageAlt={heroData.title}
      blur={{ min: -15, max: 15 }}
      strength={400}
      className="relative overflow-hidden"
      bgStyle={{
        backgroundPosition: "center center",
        backgroundSize: "cover",
        backgroundRepeat: "no-repeat",
        height: "100%",
        width: "100%",
      }}
      bgImageStyle={{
        objectFit: "cover",
        objectPosition: "center center",
        width: "100%",
        height: "100%",
      }}
    >
      {/* Gradient overlays */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/90 to-black"></div>
      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-b from-transparent via-black/80 to-black"></div>

      {/* Content */}
      <div className="relative container mx-auto flex min-h-[90vh] flex-col items-center justify-center md:justify-center md:gap-8 gap-10 p-4 pb-12 md:pb-16 text-center">
        <h1 className="text-white text-5xl font-black leading-tight tracking-[-0.033em] md:text-7xl mt-40">
          {heroData.title}
        </h1>
        <p className="max-w-xl text-lg font-light text-gray-200 md:text-xl mb-6 md:mb-8">
          {heroData.description}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
          <Button
            onClick={handleWhatsAppClick}
            size="xl"
            animation="hover"
            className="min-w-[240px] rounded-full font-bold tracking-wide h-16"
            style={
              heroData.colors?.primary
                ? {
                    backgroundColor: heroData.colors.primary,
                    color: heroData.colors.linkText || "#ffffff",
                  }
                : {
                    backgroundColor: "#ffffff",
                    color: "#000000",
                  }
            }
            leftIcon={<WhatsAppIcon className="w-8 h-8" />}
          >
            {heroData.buttonText}
          </Button>
          <Button
            onClick={handleScrollToSocialLinks}
            size="xl"
            variant="outline"
            animation="hover"
            className="min-w-[240px] rounded-full font-bold tracking-wide border-white/10 text-white hover:bg-white/20 bg-white/10 hover:text-white h-16"
            rightIcon={<ArrowDown className="w-5 h-5" />}
          >
            Ver mais
          </Button>
        </div>
      </div>
    </Parallax>
  );
};

export default HeroSection;

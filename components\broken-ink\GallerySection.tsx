"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ChevronLeft, ChevronRight, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { UserProfile } from "@/types/user";
import { transformGalleryData } from "@/lib/brokenInkUtils";

// Import PhotoSwipe styles
import "photoswipe/dist/photoswipe.css";

// Import PhotoSwipe
import PhotoSwipeLightbox from "photoswipe/lightbox";
import PhotoSwipe from "photoswipe";

interface GallerySectionProps {
  profile: UserProfile;
}

const GallerySection = ({ profile }: GallerySectionProps) => {
  const galleryData = transformGalleryData(profile);
  const galleryRef = useRef<HTMLDivElement>(null);
  const [imageDimensions, setImageDimensions] = useState<{
    [key: string]: { width: number; height: number };
  }>({});
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    align: "start",
    loop: true,
    slidesToScroll: 1,
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  // Set up embla event listeners
  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on("select", onSelect);
    emblaApi.on("reInit", onSelect);
  }, [emblaApi, onSelect]);

  // Initialize PhotoSwipe
  useEffect(() => {
    let lightbox: PhotoSwipeLightbox | null = null;

    if (
      galleryRef.current &&
      galleryData.galleryItems.length > 0 &&
      Object.keys(imageDimensions).length > 0
    ) {
      lightbox = new PhotoSwipeLightbox({
        gallery: "#photo-gallery",
        children: "a",
        pswpModule: PhotoSwipe,
        // Options
        showHideAnimationType: "fade",
        showAnimationDuration: 300,
        hideAnimationDuration: 300,
        zoom: true,
        counter: true,
        close: true,
        imageClickAction: "zoom",
        tapAction: "zoom",
      });

      lightbox.init();
    }

    return () => {
      if (lightbox) {
        lightbox.destroy();
      }
    };
  }, [galleryData.galleryItems, imageDimensions]);

  // Preload images and get their real dimensions
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions: { [key: string]: { width: number; height: number } } =
        {};

      await Promise.all(
        galleryData.galleryItems.map((item) => {
          return new Promise<void>((resolve) => {
            const img = new window.Image();
            img.onload = () => {
              dimensions[item.imageUrl] = {
                width: img.naturalWidth,
                height: img.naturalHeight,
              };
              resolve();
            };
            img.onerror = () => {
              // Fallback dimensions if image fails to load
              dimensions[item.imageUrl] = {
                width: 1200,
                height: 800,
              };
              resolve();
            };
            img.src = item.imageUrl;
          });
        })
      );

      setImageDimensions(dimensions);
    };

    if (galleryData.galleryItems.length > 0) {
      loadImageDimensions();
    }
  }, [galleryData.galleryItems]);

  if (!galleryData.enabled) {
    return null;
  }

  return (
    <section className="py-16 sm:py-24 bg-black" id="gallery">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-white text-3xl font-bold leading-tight tracking-[-0.015em] mb-4">
            {galleryData.title}
          </h2>
          {galleryData.description && (
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              {galleryData.description}
            </p>
          )}
        </div>

        <div className="relative" ref={galleryRef} id="photo-gallery">
          <div
            className="overflow-hidden rounded-xl sm:rounded-2xl"
            ref={emblaRef}
          >
            <div className="flex">
              {galleryData.galleryItems.map((item, index) => (
                <div
                  key={`${item.title}-${index}`}
                  className="flex-[0_0_100%] sm:flex-[0_0_50%] lg:flex-[0_0_33.333%] min-w-0 px-1 sm:px-2"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] transform-gpu group rounded-lg">
                    <a
                      href={item.imageUrl}
                      data-pswp-width={
                        imageDimensions[item.imageUrl]?.width || 1200
                      }
                      data-pswp-height={
                        imageDimensions[item.imageUrl]?.height || 800
                      }
                      data-pswp-caption={`<h4>${item.title}</h4><p>${item.altText}</p>`}
                      target="_blank"
                      rel="noreferrer"
                      className="block relative group cursor-pointer"
                    >
                      <Image
                        src={item.imageUrl}
                        alt={item.altText}
                        width={imageDimensions[item.imageUrl]?.width || 1200}
                        height={imageDimensions[item.imageUrl]?.height || 800}
                        className="w-full h-64 sm:h-80 lg:h-96 object-cover transition-transform duration-700 group-hover:scale-110"
                        priority={index === 0}
                        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 100vw, 100vw"
                      />
                      {/* Enhanced overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end">
                        <div className="p-4 sm:p-6 lg:p-8 text-white w-full transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                          <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-2 drop-shadow-lg">
                            {item.title}
                          </h3>
                          <p className="text-sm sm:text-base opacity-90 leading-relaxed drop-shadow-sm">
                            {item.altText}
                          </p>
                        </div>
                      </div>

                      {/* View icon overlay */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                          <Eye className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      {/* Image counter badge */}
                      <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                        <span className="text-white text-xs sm:text-sm font-medium">
                          {index + 1} / {galleryData.galleryItems.length}
                        </span>
                      </div>
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced navigation buttons */}
          <div className="flex justify-center mt-6 sm:mt-8 gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={scrollPrev}
              className="w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform bg-white/10 border-white/20 text-white hover:bg-white/20"
              aria-label="Imagem anterior"
            >
              <ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={scrollNext}
              className="w-10 h-10 sm:w-12 sm:h-12 rounded-full p-0 backdrop-blur-sm transition-all duration-200 hover:scale-110 transform bg-white/10 border-white/20 text-white hover:bg-white/20"
              aria-label="Próxima imagem"
            >
              <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
            </Button>
          </div>

          {/* Progress indicator */}
          {galleryData.galleryItems.length > 1 && (
            <div className="flex justify-center mt-4 gap-2">
              {galleryData.galleryItems.map((_, index) => (
                <div
                  key={index}
                  className="w-2 h-2 rounded-full transition-all duration-300 cursor-pointer bg-white"
                  style={{
                    opacity: selectedIndex === index ? 1 : 0.4,
                    transform:
                      selectedIndex === index ? "scale(1.25)" : "scale(1)",
                  }}
                  onMouseEnter={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.6";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedIndex !== index) {
                      e.currentTarget.style.opacity = "0.4";
                    }
                  }}
                  onClick={() => emblaApi?.scrollTo(index)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default GallerySection;

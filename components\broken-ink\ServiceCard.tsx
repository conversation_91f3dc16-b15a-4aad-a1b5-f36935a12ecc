import React from "react";

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ServiceCard: React.FC<ServiceCardProps> = ({
  icon,
  title,
  description,
}) => {
  return (
    <div className="flex flex-col items-center gap-4 rounded-3xl bg-custom/40 backdrop-blur-3xl md:w-[340px] w-full max-w-sm sm:max-w-xs p-6 ring-1 ring-black/10 transition-all hover:ring-white/20 text-center">
      <div className="bg-black/40 p-6 rounded-3xl text-white">{icon}</div>
      <h3 className="text-white text-xl font-bold">{title}</h3>
      <p className="text-gray-400">{description}</p>
    </div>
  );
};

export default ServiceCard;

import { UserProfile, ApiResponse, validateUserProfile } from '@/types'

// Define the structure for different data sections
export interface DataSection {
  id: string
  name: string
  type: 'array' | 'object'
  fields: DataField[]
  itemsKey?: string // Key name for the items array (e.g., 'items', 'images', 'reviews', 'members')
}

export interface DataField {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'textarea' | 'select'
  required?: boolean
  options?: string[] // For select fields
  validation?: (value: any) => string | null
}

// Data section definitions based on the JSON structure
export const dataSections: DataSection[] = [
  {
    id: 'featuresSection',
    name: 'Features',
    type: 'object',
    itemsKey: 'items',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'gallery',
    name: 'Gallery',
    type: 'object',
    itemsKey: 'images',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'servicesSection',
    name: 'Services',
    type: 'object',
    itemsKey: 'items',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'reviews',
    name: 'Reviews',
    type: 'object',
    itemsKey: 'reviews',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'team',
    name: 'Team',
    type: 'object',
    itemsKey: 'members',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
    ]
  },
  {
    id: 'links',
    name: 'Links',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'socialMedia',
    name: 'Social Media',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'location',
    name: 'Location',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'googleMapsUrl', label: 'Google Maps URL', type: 'url' },
    ]
  },
]

// Item field definitions for array-type sections
export const itemFieldDefinitions: Record<string, DataField[]> = {
  featuresSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  gallery: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'alt', label: 'Alt Text', type: 'string', required: true },
    { key: 'url', label: 'Image URL', type: 'url', required: true },
  ],
  servicesSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  reviews: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'comment', label: 'Comment', type: 'textarea', required: true },
    { key: 'rating', label: 'Rating', type: 'number', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
  ],
  team: [
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'role', label: 'Role', type: 'string', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
    { key: 'url', label: 'Profile URL', type: 'url' },
  ],
}

export interface CrudOperation {
  type: 'create' | 'read' | 'update' | 'delete'
  section: string
  itemId?: string | number
  data?: any
}

export interface CrudResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

class AdminDataService {
  private dataCache = new Map<string, UserProfile>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
  private readonly API_BASE_URL = process.env.NEXT_PUBLIC_FIREBASE_API_BASE_URL || 'https://cardlink-bio-default-rtdb.firebaseio.com/users'

  /**
   * Get all available usernames/profiles from Firebase
   */
  async getAvailableProfiles(): Promise<string[]> {
    try {
      const response = await fetch(`${this.API_BASE_URL}.json`)
      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`)
      }

      const data = await response.json()
      if (!data) {
        return []
      }

      // Return the keys (usernames) from the Firebase response
      return Object.keys(data)
    } catch (error) {
      console.error('Error fetching available profiles:', error)
      return []
    }
  }

  /**
   * Load user profile data from Firebase
   */
  async loadUserData(username: string): Promise<UserProfile | null> {
    try {
      // Check cache first
      const cached = this.dataCache.get(username)
      if (cached) {
        return cached
      }

      const response = await fetch(`${this.API_BASE_URL}/${username}.json`)
      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to load data: ${response.status}`)
      }

      const data = await response.json()
      if (!data) {
        return null
      }

      const validationResult = validateUserProfile(data)

      if (!validationResult.isValid) {
        console.warn('Data validation warnings:', validationResult.errors)
      }

      // Cache the data
      this.dataCache.set(username, data)

      return data
    } catch (error) {
      console.error('Error loading user data:', error)
      return null
    }
  }

  /**
   * Save user profile data to Firebase
   */
  async saveUserData(username: string, data: UserProfile): Promise<CrudResult> {
    try {
      const response = await fetch(`${this.API_BASE_URL}/${username}.json`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to save data: ${response.status}`)
      }

      // Update cache
      this.dataCache.set(username, data)

      return { success: true, message: 'Data saved successfully' }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save data'
      }
    }
  }

  /**
   * Get data for a specific section
   */
  async getSectionData(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const sectionData = (userData as any)[sectionId]
      if (sectionData === undefined) {
        return { success: false, error: 'Section not found' }
      }

      return { success: true, data: sectionData }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get items from an array-type section
   */
  async getSectionItems(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const sectionResult = await this.getSectionData(username, sectionId)
      if (!sectionResult.success) {
        return sectionResult
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(section => section.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      const items = sectionResult.data?.[itemsKey] || []
      return { success: true, data: items }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a new item in a section
   */
  async createItem(username: string, sectionId: string, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(s => s.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      // Generate new ID for items that need it
      if (itemData.id === undefined && section[itemsKey]) {
        const maxId = Math.max(0, ...section[itemsKey].map((item: any) => item.id || 0))
        itemData.id = maxId + 1
      }

      // Add item to section
      if (section[itemsKey]) {
        section[itemsKey].push(itemData)
      }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: itemData,
        message: 'Item created successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Update an existing item
   */
  async updateItem(username: string, sectionId: string, itemId: string | number, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(s => s.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      if (!section[itemsKey]) {
        return { success: false, error: 'Items array not found' }
      }

      const itemIndex = section[itemsKey].findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Update the item
      section[itemsKey][itemIndex] = { ...section[itemsKey][itemIndex], ...itemData }

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: section[itemsKey][itemIndex],
        message: 'Item updated successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete an item
   */
  async deleteItem(username: string, sectionId: string, itemId: string | number): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Find the section configuration to get the correct items key
      const sectionConfig = dataSections.find(s => s.id === sectionId)
      const itemsKey = sectionConfig?.itemsKey || 'items'

      if (!section[itemsKey]) {
        return { success: false, error: 'Items array not found' }
      }

      const itemIndex = section[itemsKey].findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Remove the item
      const deletedItem = section[itemsKey].splice(itemIndex, 1)[0]

      // Save to Firebase
      const saveResult = await this.saveUserData(username, userData)
      if (!saveResult.success) {
        return saveResult
      }

      return {
        success: true,
        data: deletedItem,
        message: 'Item deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Validate item data against field definitions
   */
  validateItemData(sectionId: string, itemData: any): { isValid: boolean; errors: string[] } {
    const fields = itemFieldDefinitions[sectionId] || []
    const errors: string[] = []

    for (const field of fields) {
      const value = itemData[field.key]

      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label} is required`)
        continue
      }

      // Type validation
      if (value !== undefined && value !== null && value !== '') {
        switch (field.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${field.label} must be a number`)
            }
            break
          case 'url':
            try {
              new URL(value)
            } catch {
              errors.push(`${field.label} must be a valid URL`)
            }
            break
          case 'email':
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              errors.push(`${field.label} must be a valid email`)
            }
            break
        }

        // Custom validation
        if (field.validation) {
          const validationError = field.validation(value)
          if (validationError) {
            errors.push(validationError)
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors }
  }
}

export const adminDataService = new AdminDataService()

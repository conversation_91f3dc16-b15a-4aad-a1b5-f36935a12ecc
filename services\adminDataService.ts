import { UserProfile, ApiResponse, validateUserProfile } from '@/types'

// Define the structure for different data sections
export interface DataSection {
  id: string
  name: string
  type: 'array' | 'object'
  fields: DataField[]
}

export interface DataField {
  key: string
  label: string
  type: 'string' | 'number' | 'boolean' | 'url' | 'email' | 'textarea' | 'select'
  required?: boolean
  options?: string[] // For select fields
  validation?: (value: any) => string | null
}

// Data section definitions based on the JSON structure
export const dataSections: DataSection[] = [
  {
    id: 'featuresSection',
    name: 'Features',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'gallery',
    name: 'Gallery',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'servicesSection',
    name: 'Services',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'reviews',
    name: 'Reviews',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
      { key: 'description', label: 'Description', type: 'textarea', required: true },
    ]
  },
  {
    id: 'team',
    name: 'Team',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'title', label: 'Title', type: 'string', required: true },
    ]
  },
  {
    id: 'links',
    name: 'Links',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'socialMedia',
    name: 'Social Media',
    type: 'array',
    fields: [
      { key: 'classIcon', label: 'Icon Class', type: 'string', required: true },
      { key: 'text', label: 'Text', type: 'string', required: true },
      { key: 'url', label: 'URL', type: 'url', required: true },
    ]
  },
  {
    id: 'location',
    name: 'Location',
    type: 'object',
    fields: [
      { key: 'enabled', label: 'Enabled', type: 'boolean' },
      { key: 'googleMapsUrl', label: 'Google Maps URL', type: 'url' },
    ]
  },
]

// Item field definitions for array-type sections
export const itemFieldDefinitions: Record<string, DataField[]> = {
  featuresSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  gallery: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'alt', label: 'Alt Text', type: 'string', required: true },
    { key: 'url', label: 'Image URL', type: 'url', required: true },
  ],
  servicesSection: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'title', label: 'Title', type: 'string', required: true },
    { key: 'description', label: 'Description', type: 'textarea', required: true },
    { key: 'image', label: 'Image URL', type: 'url', required: true },
  ],
  reviews: [
    { key: 'id', label: 'ID', type: 'number', required: true },
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'comment', label: 'Comment', type: 'textarea', required: true },
    { key: 'rating', label: 'Rating', type: 'number', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
  ],
  team: [
    { key: 'name', label: 'Name', type: 'string', required: true },
    { key: 'role', label: 'Role', type: 'string', required: true },
    { key: 'photo', label: 'Photo URL', type: 'url' },
    { key: 'url', label: 'Profile URL', type: 'url' },
  ],
}

export interface CrudOperation {
  type: 'create' | 'read' | 'update' | 'delete'
  section: string
  itemId?: string | number
  data?: any
}

export interface CrudResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

class AdminDataService {
  private dataCache = new Map<string, UserProfile>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Load user profile data
   */
  async loadUserData(username: string): Promise<UserProfile | null> {
    try {
      const response = await fetch(`/data/${username}.json`)
      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to load data: ${response.status}`)
      }

      const data = await response.json()
      const validationResult = validateUserProfile(data)
      
      if (!validationResult.isValid) {
        console.warn('Data validation warnings:', validationResult.errors)
      }

      // Cache the data
      this.dataCache.set(username, data)
      
      return data
    } catch (error) {
      console.error('Error loading user data:', error)
      return null
    }
  }

  /**
   * Get all available usernames/profiles
   */
  async getAvailableProfiles(): Promise<string[]> {
    // For now, return known profiles
    // In a real implementation, this would scan the data directory
    return ['rocusbarbearia', 'barbearia']
  }

  /**
   * Get data for a specific section
   */
  async getSectionData(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const sectionData = (userData as any)[sectionId]
      if (sectionData === undefined) {
        return { success: false, error: 'Section not found' }
      }

      return { success: true, data: sectionData }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Get items from an array-type section
   */
  async getSectionItems(username: string, sectionId: string): Promise<CrudResult> {
    try {
      const sectionResult = await this.getSectionData(username, sectionId)
      if (!sectionResult.success) {
        return sectionResult
      }

      const items = sectionResult.data?.items || []
      return { success: true, data: items }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Create a new item in a section
   */
  async createItem(username: string, sectionId: string, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section) {
        return { success: false, error: 'Section not found' }
      }

      // Generate new ID for items that need it
      if (itemData.id === undefined && section.items) {
        const maxId = Math.max(0, ...section.items.map((item: any) => item.id || 0))
        itemData.id = maxId + 1
      }

      // Add item to section
      if (section.items) {
        section.items.push(itemData)
      }

      // In a real implementation, this would save to the backend
      console.log('Would save data:', { username, sectionId, itemData })

      return { 
        success: true, 
        data: itemData, 
        message: 'Item created successfully' 
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Update an existing item
   */
  async updateItem(username: string, sectionId: string, itemId: string | number, itemData: any): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section || !section.items) {
        return { success: false, error: 'Section not found' }
      }

      const itemIndex = section.items.findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Update the item
      section.items[itemIndex] = { ...section.items[itemIndex], ...itemData }

      // In a real implementation, this would save to the backend
      console.log('Would save data:', { username, sectionId, itemId, itemData })

      return { 
        success: true, 
        data: section.items[itemIndex], 
        message: 'Item updated successfully' 
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Delete an item
   */
  async deleteItem(username: string, sectionId: string, itemId: string | number): Promise<CrudResult> {
    try {
      const userData = await this.loadUserData(username)
      if (!userData) {
        return { success: false, error: 'User data not found' }
      }

      const section = (userData as any)[sectionId]
      if (!section || !section.items) {
        return { success: false, error: 'Section not found' }
      }

      const itemIndex = section.items.findIndex((item: any) => item.id === itemId)
      if (itemIndex === -1) {
        return { success: false, error: 'Item not found' }
      }

      // Remove the item
      const deletedItem = section.items.splice(itemIndex, 1)[0]

      // In a real implementation, this would save to the backend
      console.log('Would save data:', { username, sectionId, itemId })

      return { 
        success: true, 
        data: deletedItem, 
        message: 'Item deleted successfully' 
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Validate item data against field definitions
   */
  validateItemData(sectionId: string, itemData: any): { isValid: boolean; errors: string[] } {
    const fields = itemFieldDefinitions[sectionId] || []
    const errors: string[] = []

    for (const field of fields) {
      const value = itemData[field.key]

      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label} is required`)
        continue
      }

      // Type validation
      if (value !== undefined && value !== null && value !== '') {
        switch (field.type) {
          case 'number':
            if (isNaN(Number(value))) {
              errors.push(`${field.label} must be a number`)
            }
            break
          case 'url':
            try {
              new URL(value)
            } catch {
              errors.push(`${field.label} must be a valid URL`)
            }
            break
          case 'email':
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              errors.push(`${field.label} must be a valid email`)
            }
            break
        }

        // Custom validation
        if (field.validation) {
          const validationError = field.validation(value)
          if (validationError) {
            errors.push(validationError)
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors }
  }
}

export const adminDataService = new AdminDataService()

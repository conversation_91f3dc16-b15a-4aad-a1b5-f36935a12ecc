"use client";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { useState } from "react";

// FAQ Item Component
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border border-border/50 rounded-xl overflow-hidden bg-card/50 backdrop-blur-sm">
      <button
        className="w-full px-6 py-4 sm:px-8 sm:py-6 text-left hover:bg-muted/50 transition-colors duration-200 flex items-center justify-between group"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="font-semibold text-foreground text-base sm:text-lg pr-4">
          {question}
        </h3>
        <svg
          className={`w-5 h-5 text-muted-foreground transition-transform duration-200 flex-shrink-0 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div className="px-6 pb-4 sm:px-8 sm:pb-6 animate-slide-down">
          <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
            {answer}
          </p>
        </div>
      )}
    </div>
  );
}

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 h-20 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center space-x-2 sm:space-x-3 group"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-300">
              <span className="text-primary-foreground font-bold text-xl group-hover:rotate-12 transition-transform duration-300">
                A
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-xl sm:text-2xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                AvencaLink
              </span>
              <span className="text-xs text-muted-foreground hidden sm:block">
                by Avenca Digital
              </span>
            </div>
          </Link>

          <div className="flex items-center space-x-2 sm:space-x-4">
            <ThemeToggle />
            {/* <Button
              variant="ghost"
              asChild
              animation="hover"
              className="hidden sm:flex rounded-2xl text-sm sm:text-base"
            >
              <Link href="/">Entrar</Link>
            </Button> */}
            <Button
              asChild
              animation="lift"
              size="sm"
              className="sm:size-default rounded-2xl text-sm sm:text-base"
            >
              <Link href="/">Solicitar</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 sm:px-6 py-12 sm:py-16 lg:py-20 text-center mt-8">
        {" "}
        {/* Added mt-8 to avoid overlap with sticky header */}
        <div className="max-w-5xl mx-auto space-y-6 sm:space-y-8">
          <div className="animate-fade-in">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6 group">
              <span className="w-2 h-2 bg-primary rounded-full mr-2 animate-pulse group-hover:scale-150 transition-transform duration-300"></span>
              Solução exclusiva da Avenca Digital
            </div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-extrabold text-foreground mb-6 sm:mb-8 leading-tight tracking-tight px-2">
              Sua página de Links{" "}
              <span className="text-primary">Profissional</span>:{" "}
              <span className="bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                Mais opções, menos limitações.
              </span>
            </h1>
          </div>

          <div className="animate-slide-up">
            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground mb-6 sm:mb-8 max-w-3xl mx-auto leading-relaxed text-balance px-4">
              Desenvolvemos uma solução completa para você centralizar todos os
              seus links importantes. Design moderno, recursos avançados e
              totalmente personalizável.
            </p>
            <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-10 max-w-4xl mx-auto text-muted-foreground mb-10 sm:my-20 text-left px-4 text-sm sm:text-base">
              {[
                "Domínio próprio para fortalecer sua marca.",
                "Design profissional e 100% exclusivo.",
                "Galeria, avaliações, localização, e mais.",
                "Otimização SEO para ser encontrado no Google.",
                "Carregamento ultra-rápido para não perder visitantes.",
                "Desenvolvimento e suporte da Avenca Digital.",
              ].map((item) => (
                <li key={item} className="flex items-start group">
                  <svg
                    className="w-5 h-5 text-primary mr-2.5 mt-1 flex-shrink-0 group-hover:scale-110 transition-transform"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="group-hover:text-foreground transition-colors">
                    {item}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 sm:mb-20 animate-slide-up px-4">
            <Button
              size="xl"
              asChild
              animation="pulse"
              className="w-full sm:w-auto text-base sm:text-lg px-8 py-4 rounded-xl h-16 shadow-lg font-semibold"
            >
              <Link href="/">Solicite sua página</Link>
            </Button>
            <Button
              variant="ghost"
              size="xl"
              asChild
              animation="hover"
              className="w-full sm:w-auto text-base sm:text-lg px-8 py-4 rounded-xl h-16 text-primary hover:bg-primary/10 font-medium"
            >
              <Link href="#comparativo">
                Página Própria vs. Genéricos <span className="ml-2">→</span>
              </Link>
            </Button>
          </div>

          {/* Demo Preview */}
          <div className="relative max-w-md mx-auto animate-bounce-in px-4 group">
            <div className="absolute -inset-1 bg-gradient-to-r from-primary via-purple-500 to-pink-500 rounded-3xl blur-xl opacity-30 group-hover:opacity-50 transition duration-1000 group-hover:duration-300 animate-tilt"></div>
            <div className="relative bg-card rounded-3xl shadow-strong p-6 sm:p-8 border border-border/50 backdrop-blur-sm overflow-hidden">
              <div className="absolute -top-16 -left-16 w-40 h-40 bg-primary/10 rounded-full opacity-50 blur-2xl"></div>
              <div className="absolute -bottom-16 -right-16 w-40 h-40 bg-purple-500/10 rounded-full opacity-50 blur-2xl"></div>
              <div className="relative z-10">
                <div className="text-center mb-6 sm:mb-8">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary to-purple-600 rounded-full mx-auto mb-4 sm:mb-6 shadow-lg group-hover:scale-105 transition-transform duration-300 flex items-center justify-center">
                    <svg
                      className="w-10 h-10 sm:w-12 sm:h-12 text-primary-foreground transform transition-all duration-500 group-hover:rotate-[360deg]"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="1.5"
                        d="M7 16l-4-4m0 0l4-4m-4 4h18m-7 4l4 4m0 0l4-4m-4 4v-8m0 0l4-4m-4 4H3"
                      ></path>
                    </svg>
                  </div>
                  <h3 className="font-semibold text-card-foreground text-lg sm:text-xl mb-2 group-hover:text-primary transition-colors">
                    Seu Nome
                  </h3>
                  <p className="text-sm sm:text-base text-muted-foreground mb-2">
                    @seunome
                  </p>
                  <p className="text-sm sm:text-base text-muted-foreground">
                    Sua descrição
                  </p>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  {[
                    {
                      text: "Compartilhe seus links",
                      icon: "M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4",
                      primary: true,
                    },
                    {
                      text: "Promova suas Redes Sociais",
                      icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
                    }, // Heart icon for Instagram
                    {
                      text: "Divulgue seus serviços",
                      icon: "M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z",
                    },
                    {
                      text: "Facilite o agendamento",
                      icon: "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z",
                    },
                  ].map((link, index) => (
                    <div
                      key={index}
                      className={`rounded-xl p-3.5 sm:p-4 text-sm sm:text-base transition-all duration-300 ease-out interactive cursor-pointer flex items-center gap-3 group/link-item shadow-sm hover:shadow-md
                        ${
                          link.primary
                            ? "bg-gradient-to-r from-primary via-purple-600 to-pink-500 text-primary-foreground hover:scale-[1.02]"
                            : "bg-card hover:bg-muted/80 dark:bg-muted/20 dark:hover:bg-muted/40 text-foreground hover:text-primary hover:border-primary/30 border border-transparent"
                        }`}
                    >
                      <svg
                        className={`w-5 h-5 flex-shrink-0 ${
                          link.primary ? "" : "text-primary"
                        } group-hover/link-item:scale-110 transition-transform duration-300`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d={link.icon}
                        ></path>
                      </svg>
                      <span className="flex-grow font-medium">{link.text}</span>
                      <svg
                        className={`w-4 h-4 opacity-0 group-hover/link-item:opacity-100 group-hover/link-item:translate-x-1 transition-all duration-300 ${
                          link.primary
                            ? "text-primary-foreground/80"
                            : "text-primary"
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                  ))}
                  <div className="bg-card hover:bg-muted/60 dark:bg-muted/10 dark:hover:bg-muted/30 rounded-xl p-3 text-xs transition-all duration-300 ease-out interactive cursor-pointer group/link-item shadow-sm hover:shadow-md border border-transparent hover:border-primary/30">
                    <div className="flex items-center justify-between text-muted-foreground hover:text-primary">
                      <span>⭐ Compartilhe suas Avaliações (4.9/5)</span>
                      <svg
                        className="w-3 h-3 opacity-0 group-hover/link-item:opacity-100 group-hover/link-item:translate-x-0.5 transition-all duration-300 text-primary"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            Recursos Avançados Inclusos
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
            Nossa solução oferece recursos profissionais para maximizar sua
            presença online
          </p>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 max-w-6xl mx-auto">
          <div className="text-center group animate-slide-up px-4">
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Links Ilimitados e Personalizados
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Adicione quantos links precisar, cada um com ícones, cores e
              estilos customizáveis. Crie botões, cards ou listas, com animações
              suaves que encantam seus visitantes.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.1s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-primary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
                ></path>
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Domínio Próprio e URL Amigável
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Use seu próprio domínio (ex: links.seusite.com) ou um subdomínio
              AvencaLink (ex: suaempresa.avenca.link) para reforçar sua marca e
              facilitar o compartilhamento.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.2s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-success/10 dark:bg-success/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-success/20 dark:group-hover:bg-success/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-success"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Galeria de Imagens Profissional
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Mostre seu trabalho, produtos ou portfólio com uma galeria de
              imagens elegante, com lightbox, navegação por carrossel e
              visualização em tela cheia.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.3s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-warning/10 dark:bg-warning/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-warning/20 dark:group-hover:bg-warning/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-warning"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Avaliações e Depoimentos
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Construa credibilidade exibindo avaliações e depoimentos de seus
              clientes em um carrossel interativo e com design que valoriza cada
              feedback.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.4s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-blue-500/10 dark:bg-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-blue-500/20 dark:group-hover:bg-blue-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>
            {/* Mapa e Localização */}
            {/* <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Mapa de Localização Interativo
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Facilite para seus clientes encontrarem seu estabelecimento físico
              com um mapa interativo, informações de endereço, contato e horário
              de funcionamento.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.5s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-purple-500/10 dark:bg-purple-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-purple-500/20 dark:group-hover:bg-purple-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-purple-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                />
              </svg>
            </div> */}

            {/* Design Exclusivo e Personalizado */}
            {/* <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Design Exclusivo e Personalizado
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Sua página com a cara da sua marca. Cores, fontes, layouts e temas
              totalmente customizáveis para uma identidade visual única e
              profissional.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.6s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-teal-500/10 dark:bg-teal-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-teal-500/20 dark:group-hover:bg-teal-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-teal-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                ></path>
              </svg>
            </div> */}
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Segurança e Confiabilidade
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Hospedagem robusta com SSL (HTTPS) incluso, backups automáticos e
              atualizações constantes para garantir que sua página esteja sempre
              online e segura.
            </p>
          </div>

          <div
            className="text-center group animate-slide-up px-4"
            style={{ animationDelay: "0.7s" }}
          >
            <div className="w-14 h-14 sm:w-16 sm:h-16 bg-green-500/10 dark:bg-green-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-green-500/20 dark:group-hover:bg-green-500/30 transition-all duration-300 interactive-lift">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
              Performance Otimizada e SEO
            </h3>
            <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
              Desenvolvido com Next.js e React para carregamento rápido,
              excelente experiência do usuário e otimizações para melhor
              posicionamento no Google.
            </p>
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section
        id="comparativo"
        className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24 scroll-mt-20"
      >
        <div className="text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            AvencaLink vs. Plataformas Genéricas (como Linktree)
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
            Veja por que ter uma página de links personalizada com AvencaLink
            eleva sua presença online a outro nível.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
            {/* AvencaLink Column */}
            <div className="bg-card rounded-2xl p-6 sm:p-8 border border-primary/50 shadow-lg animate-slide-up">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-md mr-3">
                  <span className="text-primary-foreground font-bold text-xl">
                    A
                  </span>
                </div>
                <h3 className="text-2xl font-semibold text-primary">
                  AvencaLink
                </h3>
              </div>
              <ul className="space-y-4">
                {[
                  {
                    text: "Domínio próprio e URL 100% personalizável (seunome.com ou links.suaempresa.com).",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Design exclusivo, alinhado com sua marca e identidade visual.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Recursos avançados: galeria de imagens profissional, sistema de avaliações de clientes, mapa de localização integrado, formulários de contato e mais.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Otimização completa para SEO, ajudando sua página a ser encontrada no Google.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Performance superior com tecnologias modernas (Next.js, React).",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Sem limitações no número de links ou recursos visuais.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Integração com Analytics para acompanhar o tráfego e comportamento dos usuários.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Suporte técnico especializado e consultoria da Avenca Digital.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Propriedade total da sua página e dados.",
                    icon: "M5 13l4 4L19 7",
                  },
                  {
                    text: "Ideal para quem busca profissionalismo, diferenciação e controle total.",
                    icon: "M5 13l4 4L19 7",
                  },
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <svg
                      className="w-6 h-6 text-green-500 mr-3 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d={item.icon}
                      />
                    </svg>
                    <span className="text-muted-foreground">{item.text}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Linktree Column */}
            <div
              className="bg-card/60 rounded-2xl p-6 sm:p-8 border border-border/50 animate-slide-up"
              style={{ animationDelay: "0.1s" }}
            >
              <div className="flex items-center mb-6">
                {/* You can add a generic link icon or Linktree-like icon here if desired */}
                <svg
                  className="w-10 h-10 text-muted-foreground mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                  ></path>
                </svg>
                <h3 className="text-2xl font-semibold text-muted-foreground">
                  Plataformas Genéricas
                </h3>
              </div>
              <ul className="space-y-4">
                {[
                  {
                    text: "Subdomínio genérico (ex: linktr.ee/seunome), sem opção de domínio próprio completo.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Opções de personalização limitadas a temas pré-definidos.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Recursos básicos, com funcionalidades avançadas geralmente em planos pagos mais caros ou inexistentes.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Menor impacto em SEO, pois o conteúdo está dentro de uma plataforma compartilhada.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Performance pode variar e ser menos otimizada.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Limitações de design e funcionalidades nos planos gratuitos ou mais básicos.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Analytics básicos, com dados mais detalhados em planos superiores.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Suporte geralmente via FAQ ou comunidade, menos personalizado.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Você não é o proprietário da plataforma, sujeito a políticas de terceiros.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                  {
                    text: "Indicado para quem busca uma solução rápida e simples, com menos foco em branding e diferenciação.",
                    icon: "M6 18L18 6M6 6l12 12",
                  },
                ].map((item, index) => (
                  <li key={index} className="flex items-start">
                    <svg
                      className="w-6 h-6 text-red-500 mr-3 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d={item.icon}
                      />
                    </svg>
                    <span className="text-muted-foreground">{item.text}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Domain Checker Section */}
      <section
        id="domain-checker"
        className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24 scroll-mt-20"
      >
        <div className="max-w-3xl mx-auto text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            Escolha Seu Endereço Exclusivo na Web
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground leading-relaxed px-4">
            Verifique a disponibilidade do seu nome de usuário ou domínio
            personalizado para sua página AvencaLink. Comece a construir sua
            marca online com um endereço memorável!
          </p>
        </div>

        <div className="max-w-xl mx-auto animate-slide-up">
          <div className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-lg">
            <div className="flex flex-col sm:flex-row items-end gap-4">
              <div className="flex-grow w-full">
                <label
                  htmlFor="domain-input"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  Seu nome de usuário ou domínio:
                </label>
                <div className="relative">
                  <input
                    type="text"
                    id="domain-input"
                    // value={domainName}
                    // onChange={handleDomainChange}
                    placeholder="ex: suaempresa ou links.suaempresa.com"
                    className="w-full px-4 py-3 rounded-xl border border-border focus:ring-2 focus:ring-primary focus:border-primary transition-colors duration-200 bg-background text-foreground"
                  />
                  {/* <div className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-muted-foreground">
                    .avenca.link (opcional)
                  </div> */}
                </div>
              </div>
              <Button
                // onClick={handleDomainCheck}
                // disabled={checkingDomain}
                size="lg"
                className="w-full sm:w-auto min-w-[150px] h-[50px] rounded-xl"
              >
                {/* {checkingDomain ? (
                  <LoadingSpinner className="w-5 h-5" />
                ) : (
                  "Verificar"
                )} */}
                Verificar
              </Button>
            </div>
            {/* {domainStatus && (
              <div className={`mt-4 p-3 rounded-lg text-sm ${domainStatus.available ? 'bg-success/10 text-success-foreground' : 'bg-destructive/10 text-destructive-foreground'}`}>
                {domainStatus.message}
                {domainStatus.available && (
                  <Button asChild size="sm" className="ml-4 mt-2 sm:mt-0">
                    <Link href={`/signup?domain=${domainName}`}>Registrar este domínio</Link>
                  </Button>
                )}
              </div>
            )} */}
            <p className="text-xs text-muted-foreground mt-4 text-center">
              A disponibilidade real será confirmada durante o processo de
              solicitação.
            </p>
          </div>
        </div>
      </section>

      {/* Como Funciona Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-muted/20 to-muted/5">
        <div className="text-center mb-12 sm:mb-16 animate-fade-in">
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 px-4">
            Nosso Processo
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed px-4">
            Processo profissional da Avenca Digital para criar sua presença
            online
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 sm:gap-16 max-w-7xl mx-auto">
          {/* Esquerda - Processo */}
          <div className="space-y-8">
            <div className="flex items-start space-x-4 animate-slide-up group">
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                1
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl group-hover:text-primary transition-colors duration-300">
                  Estratégia Personalizada
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Iniciamos com uma conversa para entender profundamente seus
                  objetivos, público e identidade de marca. Com base nisso,
                  traçamos uma estratégia clara para sua página, garantindo que
                  ela não seja apenas bonita, mas também eficaz.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up group"
              style={{ animationDelay: "0.1s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                2
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl group-hover:text-primary transition-colors duration-300">
                  Design e Desenvolvimento
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Nossos designers criam um layout único que reflete sua marca.
                  Em seguida, nossos desenvolvedores transformam esse design em
                  uma página funcional, responsiva e otimizada, utilizando as
                  tecnologias mais recentes para performance e SEO.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up group"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                3
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl group-hover:text-primary transition-colors duration-300">
                  Implementação de Recursos Avançados
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Configuramos todos os recursos que você precisa: links
                  personalizados, galerias, sistema de avaliações, localização e
                  integrações. Tudo é testado para garantir uma experiência de
                  usuário impecável.
                </p>
              </div>
            </div>

            <div
              className="flex items-start space-x-4 animate-slide-up group"
              style={{ animationDelay: "0.3s" }}
            >
              <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-bold text-lg flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                4
              </div>
              <div>
                <h3 className="font-semibold text-foreground mb-2 text-xl group-hover:text-primary transition-colors duration-300">
                  Lançamento, Otimização & Suporte Contínuo
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  Sua página é lançada com seu domínio personalizado e suporte
                  técnico contínuo para garantir que sua página esteja sempre
                  performando no seu melhor.
                </p>
              </div>
            </div>
          </div>

          {/* Direita - Recursos */}
          <div className="space-y-6 sm:space-y-8">
            <div className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up">
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-success/10 dark:bg-success/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-success"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Hospedagem Gratuita
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Hospedagem profissional incluída com CDN global, SSL automático
                e 99.9% de uptime. Sem custos adicionais.
              </p>
            </div>

            <div
              className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up"
              style={{ animationDelay: "0.1s" }}
            >
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-primary/10 dark:bg-primary/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-primary"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Domínio Personalizado
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Use seu próprio domínio ou escolha um subdomínio gratuito.
                Configuração automática de DNS e certificado SSL.
              </p>
            </div>

            {/* <div
              className="bg-card rounded-2xl p-6 sm:p-8 border border-border/50 shadow-md animate-slide-up"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-warning/10 dark:bg-warning/20 rounded-2xl flex items-center justify-center mb-4 sm:mb-6">
                <svg
                  className="w-6 h-6 sm:w-8 sm:h-8 text-warning"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-foreground mb-2 sm:mb-3 text-lg sm:text-xl">
                Analytics Avançados
              </h3>
              <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                Acompanhe cliques, visitantes e performance dos seus links com
                relatórios detalhados e insights em tempo real.
              </p>
            </div> */}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12 sm:mb-16 animate-fade-in">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6">
              Perguntas Frequentes
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Esclarecemos as principais dúvidas sobre nosso serviço
            </p>
          </div>

          <div className="space-y-4 sm:space-y-6">
            <FAQItem
              question="O que é o AvencaLink e para quem é ideal?"
              answer="O AvencaLink é uma solução completa para criar páginas de links personalizadas e profissionais. É ideal para influenciadores digitais, criadores de conteúdo, empresas, profissionais liberais, artistas e qualquer pessoa ou marca que deseje centralizar seus links importantes de forma elegante, otimizada e com identidade própria, fugindo das limitações de plataformas genéricas."
            />
            <FAQItem
              question="Qual a principal diferença entre AvencaLink e o Linktree (ou similares)?"
              answer="A principal diferença está na personalização, profissionalismo e propriedade. Com AvencaLink, você tem design exclusivo, domínio próprio (ex: links.seusite.com), recursos avançados (galeria, avaliações, mapa), otimização para SEO e suporte especializado. Enquanto plataformas genéricas oferecem templates limitados e subdomínios (ex: linktr.ee/seunome), o AvencaLink proporciona uma solução sob medida para sua marca se destacar."
            />
            <FAQItem
              question="Preciso ter conhecimentos técnicos para ter uma página AvencaLink?"
              answer="Não! Nossa equipe na Avenca Digital cuida de todo o processo de design, desenvolvimento, configuração e lançamento. Se você optar por um painel de gerenciamento, ele será intuitivo e fácil de usar. Nosso objetivo é que você tenha uma página incrível sem se preocupar com a parte técnica."
            />
            <FAQItem
              question="Posso usar meu próprio domínio (ex: links.minhamarca.com)?"
              answer="Sim! Recomendamos fortemente o uso de um domínio personalizado para reforçar sua marca. Nós auxiliamos em toda a configuração. Caso não tenha um, podemos ajudar a registrar ou oferecer um subdomínio gratuito (ex: seunome.avenca.link)."
            />
            <FAQItem
              question="Minha página AvencaLink será otimizada para SEO (Google)?"
              answer="Com certeza! Nossas páginas são construídas com as melhores práticas de SEO em mente, utilizando tecnologias como Next.js que favorecem a indexação e o ranking nos motores de busca. Isso ajuda mais pessoas a encontrarem seus links."
            />
            <FAQItem
              question="A página será responsiva e funcionará bem em celulares?"
              answer="Absolutamente. Todas as páginas AvencaLink são desenvolvidas com design responsivo (mobile-first), garantindo uma experiência perfeita em smartphones, tablets e desktops. Testamos exaustivamente para assegurar a compatibilidade e performance."
            />
            <FAQItem
              question="Quais recursos avançados posso incluir na minha página?"
              answer="Oferecemos uma vasta gama de recursos: links ilimitados, galeria de imagens profissional (com lightbox e carrossel), sistema de avaliações de clientes, mapa de localização interativo (em breve), integração com redes sociais, formulários de contato, botões de CTA (Call to Action) e muito mais. Adaptamos os recursos às suas necessidades."
            />
            <FAQItem
              question="Como funciona a hospedagem e manutenção da minha página?"
              answer="Nós cuidamos de tudo! Sua página AvencaLink inclui hospedagem de alta performance com CDN global, certificado de segurança SSL (HTTPS), backups automáticos, monitoramento 24/7 e atualizações de segurança. Você não precisa se preocupar com infraestrutura."
            />
            <FAQItem
              question="Quanto tempo leva para minha página AvencaLink ficar pronta?"
              answer="O tempo de desenvolvimento varia conforme a complexidade e os recursos escolhidos. Após a definição do escopo, apresentamos um cronograma detalhado. Geralmente, páginas mais simples podem ser lançadas rapidamente, enquanto projetos mais elaborados demandam um pouco mais de tempo para garantir a qualidade."
            />
            <FAQItem
              question="Qual o investimento para ter uma página AvencaLink?"
              answer="O valor do investimento depende dos recursos, nível de personalização e complexidade do projeto. Entendemos que cada cliente é único, por isso oferecemos orçamentos personalizados. Entre em contato conosco para conversarmos sobre suas necessidades e apresentarmos uma proposta que caiba no seu orçamento e entregue o máximo de valor."
            />
            <FAQItem
              question="Terei suporte após o lançamento da página?"
              answer="Sim! A Avenca Digital preza pelo relacionamento de longo prazo. Oferecemos suporte técnico contínuo para resolver quaisquer dúvidas ou problemas, além de podermos auxiliar com futuras atualizações e novas funcionalidades conforme sua necessidade evoluir."
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 sm:px-6 py-16 sm:py-20 lg:py-24">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl sm:rounded-3xl p-8 sm:p-10 lg:p-12 shadow-strong">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-primary-foreground mb-4 sm:mb-6 px-4">
              Pronto para ter sua página profissional?
            </h2>
            <p className="text-base sm:text-lg text-primary-foreground/90 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-4">
              Entre em contato com a Avenca Digital e tenha uma página de links
              exclusiva, moderna e otimizada para conversão.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
              <Button
                size="lg"
                asChild
                variant="secondary"
                className="w-full sm:w-auto sm:min-w-[240px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-white/90 text-primary rounded-2xl h-14"
              >
                <Link href="/signup">Solicitar Orçamento</Link>
              </Button>
              <Button
                size="lg"
                asChild
                variant="outline"
                className="w-full sm:w-auto sm:min-w-[240px] text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 bg-white hover:bg-white/90 text-primary rounded-2xl h-14"
              >
                <Link href="/demo">Ver Demonstração</Link>
              </Button>
            </div>

            <div className="mt-8 pt-6 border-t border-primary-foreground/20">
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-primary-foreground/80 text-sm">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Hospedagem e SSL inclusos
                </div>
                <div className="flex items-center gap-2">
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Personalização completa
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/50 py-8 sm:py-12 mt-16 sm:mt-20 lg:mt-24">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <div className="flex items-center justify-center space-x-2 sm:space-x-3 mb-4 sm:mb-6">
            <div className="w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-primary-foreground font-bold text-sm">
                A
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="text-base sm:text-lg font-semibold text-foreground">
                AvencaLink
              </span>
              <span className="text-xs text-muted-foreground">
                by Avenca Digital
              </span>
            </div>
          </div>
          <p className="text-muted-foreground text-xs sm:text-sm max-w-md mx-auto leading-relaxed px-4">
            © 2025 Avenca Digital. Todos os direitos reservados.
            <br />
            Soluções digitais profissionais para sua presença online.
          </p>
        </div>
      </footer>
    </div>
  );
}

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { adminDataService } from '@/services/adminDataService'
import { 
  Users, 
  RefreshCw, 
  Search,
  User,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

export interface UserSelectorProps {
  selectedUser: string | null
  onUserSelect: (username: string) => void
  onUserChange?: (username: string) => void
}

interface UserInfo {
  username: string
  displayName?: string
  lastUpdated?: string
  recordCount?: number
  isOnline?: boolean
}

export default function UserSelector({ 
  selectedUser, 
  onUserSelect, 
  onUserChange 
}: UserSelectorProps) {
  const [users, setUsers] = useState<UserInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)

  // Load available users
  const loadUsers = async () => {
    try {
      setError(null)
      const usernames = await adminDataService.getAvailableProfiles()
      
      // Load basic info for each user
      const userInfoPromises = usernames.map(async (username) => {
        try {
          const userData = await adminDataService.loadUserData(username)
          return {
            username,
            displayName: userData?.user?.name || username,
            lastUpdated: new Date().toISOString(), // In real app, get from API
            recordCount: calculateRecordCount(userData),
            isOnline: true // In real app, check user status
          }
        } catch (error) {
          return {
            username,
            displayName: username,
            lastUpdated: 'Unknown',
            recordCount: 0,
            isOnline: false
          }
        }
      })

      const userInfos = await Promise.all(userInfoPromises)
      setUsers(userInfos)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load users')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // Calculate total record count for a user
  const calculateRecordCount = (userData: any): number => {
    if (!userData) return 0
    
    let count = 0
    const sections = ['featuresSection', 'gallery', 'servicesSection', 'reviews', 'team', 'links', 'socialMedia']
    
    sections.forEach(section => {
      if (userData[section]?.items) {
        count += userData[section].items.length
      }
    })
    
    return count
  }

  // Handle user selection
  const handleUserSelect = (username: string) => {
    onUserSelect(username)
    onUserChange?.(username)
  }

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadUsers()
  }

  // Load users on component mount
  useEffect(() => {
    loadUsers()
  }, [])

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading users...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
            <div className="text-destructive mb-4">Error loading users</div>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={loadUsers}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            <CardTitle className="text-lg">Select User</CardTitle>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {users.length === 0 ? (
          <div className="text-center py-8">
            <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Users Found</h3>
            <p className="text-muted-foreground mb-4">
              No users found in the Firebase database.
            </p>
            <Button onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {users.map((user) => {
              const isSelected = selectedUser === user.username
              
              return (
                <div
                  key={user.username}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-accent ${
                    isSelected ? 'border-primary bg-accent' : 'border-border'
                  }`}
                  onClick={() => handleUserSelect(user.username)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <User className="h-8 w-8 text-muted-foreground" />
                        {user.isOnline && (
                          <div className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-background"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{user.displayName}</span>
                          {isSelected && (
                            <CheckCircle className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">@{user.username}</div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <Badge variant="secondary" className="mb-1">
                        {user.recordCount} records
                      </Badge>
                      <div className="text-xs text-muted-foreground">
                        {user.isOnline ? 'Online' : 'Offline'}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}

        {selectedUser && (
          <div className="mt-6 p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-primary" />
              <span className="font-medium">Selected User</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Managing data for <strong>@{selectedUser}</strong>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import SectionManager from "@/components/admin/SectionManager";
import {
  Settings,
  Users,
  Image,
  Star,
  Link,
  MapPin,
  Palette,
  Search,
  Plus,
  Filter,
  Download,
  Upload,
} from "lucide-react";

// Data section types for navigation
const dataSections = [
  {
    id: "featuresSection",
    name: "Features",
    icon: Star,
    description: "Manage feature items and services",
  },
  {
    id: "gallery",
    name: "Gallery",
    icon: Image,
    description: "Manage gallery images and media",
  },
  {
    id: "servicesSection",
    name: "Services",
    icon: Settings,
    description: "Manage service offerings",
  },
  {
    id: "reviews",
    name: "Reviews",
    icon: Star,
    description: "Manage customer reviews and ratings",
  },
  {
    id: "team",
    name: "Team",
    icon: Users,
    description: "Manage team members and staff",
  },
  {
    id: "links",
    name: "<PERSON><PERSON>",
    icon: Link,
    description: "Manage navigation and social links",
  },
  {
    id: "socialMedia",
    name: "Social Media",
    icon: Link,
    description: "Manage social media links",
  },
  {
    id: "location",
    name: "Location",
    icon: MapPin,
    description: "Manage location and contact info",
  },
  {
    id: "settings",
    name: "Settings",
    icon: Palette,
    description: "Manage site settings and appearance",
  },
];

interface AdminDashboardProps {}

export default function AdminDashboard({}: AdminDashboardProps) {
  const [activeSection, setActiveSection] = useState<string>("featuresSection");
  const [searchQuery, setSearchQuery] = useState("");

  const handleSectionChange = (sectionId: string) => {
    setActiveSection(sectionId);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">
                Admin Dashboard
              </h1>
              <p className="text-sm text-muted-foreground">
                Manage your API data and content
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import Data
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add New
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Data Sections</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {dataSections.map((section) => {
                    const Icon = section.icon;
                    const isActive = activeSection === section.id;

                    return (
                      <button
                        key={section.id}
                        onClick={() => handleSectionChange(section.id)}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-colors hover:bg-accent ${
                          isActive
                            ? "bg-accent text-accent-foreground border-r-2 border-primary"
                            : "text-muted-foreground"
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <div className="flex-1">
                          <div className="font-medium">{section.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {section.description}
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </nav>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Total Records
                  </span>
                  <Badge variant="secondary">156</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Last Updated
                  </span>
                  <Badge variant="outline">2 hours ago</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">
                    Data Size
                  </span>
                  <Badge variant="outline">2.4 MB</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {/* Search and Filter Bar */}
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder={`Search ${
                        dataSections
                          .find((s) => s.id === activeSection)
                          ?.name.toLowerCase() || "records"
                      }...`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Dynamic Content Area */}
            <div>
              <SectionManager
                sectionId={activeSection}
                username="rocusbarbearia"
                onDataChange={() => {
                  // Handle data changes if needed
                  console.log("Data changed for section:", activeSection);
                }}
              />
          </div>
        </div>
      </div>
    </div>
  );
}

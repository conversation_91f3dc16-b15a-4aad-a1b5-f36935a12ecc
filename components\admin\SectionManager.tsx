'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ConfirmModal } from '@/components/ui/modal'
import DataTable, { DataTableColumn, DataTableAction } from './DataTable'
import { ItemForm } from './DynamicForm'
import { 
  adminDataService, 
  itemFieldDefinitions, 
  DataField 
} from '@/services/adminDataService'
import { Edit, Trash2, Eye, Plus, Settings, ToggleLeft, ToggleRight } from 'lucide-react'

export interface SectionManagerProps {
  sectionId: string
  username: string
  onDataChange?: () => void
}

export default function SectionManager({ 
  sectionId, 
  username = 'rocusbarbearia',
  onDataChange 
}: SectionManagerProps) {
  const [sectionData, setSectionData] = useState<any>(null)
  const [items, setItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Modal states
  const [showItemForm, setShowItemForm] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deletingItem, setDeletingItem] = useState<any>(null)
  
  // Get field definitions for this section
  const fields = itemFieldDefinitions[sectionId] || []
  
  // Load section data
  const loadSectionData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await adminDataService.getSectionData(username, sectionId)
      if (result.success) {
        setSectionData(result.data)
        
        // Load items if this is an array-type section
        if (result.data?.items) {
          setItems(result.data.items)
        } else {
          setItems([])
        }
      } else {
        setError(result.error || 'Failed to load section data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadSectionData()
  }, [sectionId, username])

  // Define table columns based on field definitions
  const columns: DataTableColumn[] = fields.map(field => ({
    key: field.key,
    label: field.label,
    type: field.type === 'textarea' ? 'string' : field.type,
    sortable: true,
    render: field.type === 'url' ? (value: string) => (
      value ? (
        <a 
          href={value} 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline truncate max-w-xs block"
        >
          {value.length > 50 ? `${value.substring(0, 50)}...` : value}
        </a>
      ) : null
    ) : field.type === 'textarea' ? (value: string) => (
      <div className="max-w-xs truncate" title={value}>
        {value}
      </div>
    ) : undefined
  }))

  // Define table actions
  const actions: DataTableAction[] = [
    {
      label: 'Edit',
      icon: Edit,
      onClick: (item) => {
        setEditingItem(item)
        setShowItemForm(true)
      },
      variant: 'outline'
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (item) => {
        setDeletingItem(item)
        setShowDeleteConfirm(true)
      },
      variant: 'destructive'
    }
  ]

  // Handle item creation/update
  const handleItemSubmit = async (formData: any) => {
    try {
      let result
      
      if (editingItem) {
        // Update existing item
        result = await adminDataService.updateItem(username, sectionId, editingItem.id, formData)
      } else {
        // Create new item
        result = await adminDataService.createItem(username, sectionId, formData)
      }

      if (result.success) {
        await loadSectionData() // Reload data
        setShowItemForm(false)
        setEditingItem(null)
        onDataChange?.()
        return { success: true }
      } else {
        return { success: false, error: result.error }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      }
    }
  }

  // Handle item deletion
  const handleItemDelete = async () => {
    if (!deletingItem) return

    try {
      const result = await adminDataService.deleteItem(username, sectionId, deletingItem.id)
      
      if (result.success) {
        await loadSectionData() // Reload data
        setShowDeleteConfirm(false)
        setDeletingItem(null)
        onDataChange?.()
      } else {
        setError(result.error || 'Failed to delete item')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
    }
  }

  // Handle section toggle (enable/disable)
  const handleSectionToggle = async () => {
    if (!sectionData) return

    try {
      // This would update the section's enabled status
      // For now, just toggle locally
      const newEnabled = !sectionData.enabled
      setSectionData(prev => ({ ...prev, enabled: newEnabled }))
      onDataChange?.()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to toggle section')
    }
  }

  const getSectionDisplayName = () => {
    return sectionId.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading {getSectionDisplayName()}...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <div className="text-destructive mb-4">Error loading {getSectionDisplayName()}</div>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={loadSectionData}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-3">
                {getSectionDisplayName()}
                {sectionData?.enabled !== undefined && (
                  <Badge variant={sectionData.enabled ? 'default' : 'secondary'}>
                    {sectionData.enabled ? 'Enabled' : 'Disabled'}
                  </Badge>
                )}
              </CardTitle>
              {sectionData?.title && (
                <p className="text-sm text-muted-foreground mt-1">{sectionData.title}</p>
              )}
              {sectionData?.description && (
                <p className="text-sm text-muted-foreground mt-1">{sectionData.description}</p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {sectionData?.enabled !== undefined && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSectionToggle}
                >
                  {sectionData.enabled ? (
                    <>
                      <ToggleRight className="h-4 w-4 mr-2" />
                      Disable
                    </>
                  ) : (
                    <>
                      <ToggleLeft className="h-4 w-4 mr-2" />
                      Enable
                    </>
                  )}
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
              >
                <Settings className="h-4 w-4 mr-2" />
                Section Settings
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Items Table */}
      {fields.length > 0 ? (
        <DataTable
          data={items}
          columns={columns}
          actions={actions}
          title={`${getSectionDisplayName()} Items`}
          description={`Manage items in the ${getSectionDisplayName().toLowerCase()} section`}
          onAdd={() => {
            setEditingItem(null)
            setShowItemForm(true)
          }}
          searchable
          filterable
          selectable
          pagination
          emptyMessage={`No ${getSectionDisplayName().toLowerCase()} items found. Click "Add New" to create your first item.`}
        />
      ) : (
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <Settings className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Section Configuration</h3>
              <p className="text-muted-foreground mb-6">
                This section contains configuration data rather than individual items.
              </p>
              <Button variant="outline">
                Edit Section Configuration
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Item Form Modal */}
      {showItemForm && (
        <ItemForm
          sectionId={sectionId}
          fields={fields}
          initialData={editingItem}
          onSubmit={handleItemSubmit}
          open={showItemForm}
          onOpenChange={setShowItemForm}
        />
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Delete Item"
        description={`Are you sure you want to delete "${deletingItem?.title || deletingItem?.name || 'this item'}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        onConfirm={handleItemDelete}
        onCancel={() => {
          setShowDeleteConfirm(false)
          setDeletingItem(null)
        }}
      />
    </div>
  )
}
